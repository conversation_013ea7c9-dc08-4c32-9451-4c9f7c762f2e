# SurvStat Loader

A Python-based data collection and processing system for German infectious disease surveillance data from the Robert Koch Institute's (RKI) SurvStat database.

## 📋 Overview

This project automates the collection, processing, and harmonization of infectious disease surveillance data from Germany's SurvStat system. It covers 21 different infectious diseases with historical data spanning from 2001 to the present year.

### Supported Diseases

- **Viral Diseases**: Measles, Mumps, Rubella, Chickenpox, RSV, Enterovirus, Influenza
- **Bacterial Diseases**: Pertussis (Whooping Cough), Campylobacteriosis, Pneumococcal Disease, Gonorrhea, Salmonellosis, Scarlet Fever, Tuberculosis, Haemophilus influenzae
- **Hepatitis**: Hepatitis A, B, C, E
- **Vector-borne**: Tick-borne Encephalitis

## 🚀 Features

- **Automated Web Scraping**: Uses Selenium to automatically download data from SurvStat
- **Data Processing Pipeline**: Comprehensive data cleaning and harmonization
- **Geographic Harmonization**: German county name mapping and standardization
- **Multi-year Collection**: Supports bulk downloading of historical data (2001-present)
- **Progress Tracking**: Built-in logging and progress bars for long-running operations
- **Flexible Data Processing**: Chainable data processing operations with method registry

## 📁 Project Structure

```
survstat_loader/
├── src/
│   ├── dataprocessor/          # Data processing and transformation
│   │   ├── dataprocessor.py    # Main DataProcessingOrchestrator class
│   │   ├── filtering.py        # Data filtering utilities
│   │   └── mappings.py         # Data mapping configurations
│   ├── survstat_collecting/    # Web scraping and data collection
│   │   ├── survstat_scraper.py # Selenium-based web scraper
│   │   └── casedata_processing.py # Raw data preprocessing
│   ├── utils/                  # Utility functions and configurations
│   │   ├── dirs.py            # Directory management
│   │   ├── libs.py            # Library imports
│   │   ├── logger.py          # Logging functionality
│   │   ├── mappings.py        # Geographic mappings
│   │   └── germany_harm.py    # German harmonization data
│   └── update_survstatdata.py  # Main execution script
├── data/
│   ├── raw/                   # Raw downloaded data
│   ├── preprocessed/          # Processed data
│   └── harmonization/         # Geographic harmonization files
├── log.txt                    # Execution log
├── requirements.txt           # Python dependencies
└── README.md                  # This file
```

## 🛠️ Installation

### Prerequisites

- Python 3.8 or higher
- Chrome browser (for Selenium web scraping)
- Internet connection for data downloading

### Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd survstat_loader
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Verify Chrome installation**:
   The scraper uses Chrome WebDriver, which will be automatically managed by `webdriver-manager`.

## 🎯 Usage

### Quick Start

Run the main data collection script:

```bash
cd src
python update_survstatdata.py
```

This will:
1. Read the disease configuration from the log file
2. Download the latest year's data for all configured diseases
3. Process and harmonize the raw data
4. Update the execution log

### Data Processing Pipeline

The project uses a `DataProcessingOrchestrator` class that supports method chaining:

```python
from dataprocessor import DataProcessingOrchestrator

# Example data processing pipeline
processed_data = (
    DataProcessingOrchestrator(name='disease_data')
    .import_data(filename='measles_2024.csv', directory='data/raw/measles')
    .filter(conditions=[('age_group', ['0-4', '5-9'], 'in')])
    .select(colnames=['date', 'county', 'cases'])
    .change_dtype({'cases': 'int'})
    .save_data(filename='processed_measles.csv', directory='data/processed')
)
```

### Configuration

Disease configurations are managed in the `log.txt` file. The current configuration includes:

- Disease name mappings (German ↔ English)
- Data collection years (2001-2025)
- Last execution timestamp

## 📊 Data Output

### Raw Data
- **Location**: `data/raw/{disease_name}/`
- **Format**: CSV files named `{disease}_{year}.csv`
- **Content**: Unprocessed data as downloaded from SurvStat

### Processed Data
- **Location**: `data/preprocessed/{disease_name}/`
- **Format**: Cleaned and harmonized CSV files
- **Content**: Standardized column names, geographic harmonization, data type corrections

### Geographic Harmonization
- **File**: `data/harmonization/harmfile_germany.tsv`
- **Content**: Mapping between German county names (German/English) and standardized tokens

## 🔧 Advanced Usage

### Custom Disease Collection

To add new diseases or modify the collection:

1. Update the disease mapping in the logging system
2. Ensure the disease name matches SurvStat's naming convention
3. Run the collection script

### Data Processing Customization

The `DataProcessingOrchestrator` supports various operations:

- **Data Import**: CSV, Excel, Shapefile support
- **Filtering**: Conditional row filtering
- **Transformation**: Column selection, renaming, type conversion
- **Aggregation**: Grouping and summarization
- **Geographic Operations**: Spatial joins and operations (via GeoPandas)

### Batch Processing

For processing multiple years or diseases:

```python
from survstat_collecting.survstat_scraper import scrape_survstat_data
from survstat_collecting.casedata_processing import preprocess_survstat_data

# Collect data for specific years
scrape_survstat_data(
    disease_names={'Measles': 'measles'},
    years=['2020', '2021', '2022'],
    output_directory='data/raw',
    downloads_path='/path/to/downloads'
)
```

## 📝 Logging

The system maintains detailed logs in `log.txt`:

- Execution timestamps
- Disease configurations
- Year ranges processed
- Success/failure status

## ⚠️ Important Notes

### Web Scraping Considerations

- **Rate Limiting**: The scraper includes delays to avoid overwhelming the SurvStat server
- **Browser Dependencies**: Requires Chrome browser; WebDriver is managed automatically
- **Network Dependency**: Requires stable internet connection for data downloads
- **SurvStat Changes**: Web scraping may break if SurvStat updates their interface

### Data Considerations

- **Data Availability**: Not all diseases may have data for all years
- **Geographic Changes**: German administrative boundaries may change over time
- **Data Quality**: Raw data may contain inconsistencies that require manual review

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

[Add your license information here]

## 🆘 Support

For issues or questions:

1. Check the `log.txt` file for execution details
2. Verify Chrome browser installation
3. Ensure stable internet connection
4. Check SurvStat website accessibility

## 🔄 Recent Updates

**Last Run**: 2025-07-05 12:26:16
- Successfully collected data for all 21 diseases
- Processed 25 years of historical data (2001-2025)
- Updated geographic harmonization mappings